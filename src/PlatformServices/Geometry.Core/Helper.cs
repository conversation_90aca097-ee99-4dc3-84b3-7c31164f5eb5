namespace Geometry.Core
{
    public static class Helper
    {
        public static bool IsPointInPolygon(List<PointD> polygon, PointD point, double delta = 0.0001)
        {
            int i, j;
            bool result = false;

            for (i = 0, j = polygon.Count - 1; i < polygon.Count; j = i++)
            {
                // Slightly reducing the coordinates of the vertices of the polygon 
                var xi = polygon[i].X + delta;
                var yi = polygon[i].Y + delta;
                var xj = polygon[j].X + delta;
                var yj = polygon[j].Y + delta;

                if ((yi > point.Y) != (yj > point.Y) &&
                    (point.X < (xj - xi) * (point.Y - yi) / (yj - yi) + xi))
                {
                    result = !result;
                }
            }

            return result;
        }

        public static List<double> FindXDoesIntersectPolygon(double y, List<PointD> polygon)
        {
            var intersectingPoints = new List<double>();

            var prevIndex = polygon.Count - 1;

            for (int i = 0; i < polygon.Count; i++)
            {
                var currentIndex = i;

                var startX = polygon[prevIndex].X;
                var startY = polygon[prevIndex].Y;
                var endX = polygon[currentIndex].X;
                var endY = polygon[currentIndex].Y;

                // Check if the line segment intersects with the horizontal line at coordinate y
                if ((startY <= y && endY >= y) || (startY >= y && endY <= y))
                {
                    // Calculate the x-coordinate of the intersection point using linear interpolation
                    var intersectX = startX + (y - startY) * (endX - startX) / (endY - startY);

                    // Add the intersecting point if it is not already present in the list
                    if (!intersectingPoints.Exists(p => p == intersectX))
                    {
                        intersectingPoints.Add(intersectX);
                    }
                }

                prevIndex = currentIndex;
            }

            return intersectingPoints;
        }

        public static bool CheckIfLineIntersectsPolygon(PointD start, PointD end, List<PointD> polygon)
        {
            if (!IsPointInPolygon(polygon, start) || !IsPointInPolygon(polygon, end))
            {
                return true;
            }

            // Calculate the distance between start and end points
            var distance = Math.Sqrt(Math.Pow(end.X - start.X, 2) + Math.Pow(end.Y - start.Y, 2));

            // Divide the distance by the desired density of points to check
            var steps = (int)Math.Round(distance / 0.01);

            // Generate and check each point on the line for intersection with the polygon
            for (int i = 0; i <= steps; i++)
            {
                var ratio = (double)i / steps;
                var pointOnLine = new PointD((1 - ratio) * start.X + ratio * end.X, (1 - ratio) * start.Y + ratio * end.Y);

                if (!IsPointInPolygon(polygon, pointOnLine))
                {
                    return true;
                }
            }

            return false;
        }

        public static double FindYOnPolygonEdge(double x, List<PointD> polygon)
        {
            int i, j;
            var possibilitiesOfY = new List<double>();

            for (i = 0, j = polygon.Count - 1; i < polygon.Count; j = i++)
            {
                var startX = polygon[j].X;
                var startY = polygon[j].Y;
                var endX = polygon[i].X;
                var endY = polygon[i].Y;

                if ((x >= startX && x <= endX) || (x <= startX && x >= endX))
                {
                    var slope = (endY - startY) / (endX - startX);
                    var y = startY + slope * (x - startX);

                    possibilitiesOfY.Add(y);
                }
            }

            if (possibilitiesOfY.Any())
            {
                return possibilitiesOfY.Max();
            }

            return polygon.OrderBy(x => x.X).ThenByDescending(x => x.Y).First().Y;
        }

        public static double GetInstrumentXPoint(
           PointD referenceOriginPoint,
           PointD sectionUpstreamPoint,
           PointD sectionDownstreamPoint,
           PointD sectionMidpoint,
           PointD instrumentPoint)
        {
            var upstreamToMidpointInstrumentProjection = CalculateIntersectionPoint(instrumentPoint, sectionUpstreamPoint, sectionMidpoint);
            var distanceFromUpstreamToUpstreamProjection = GetDistance(upstreamToMidpointInstrumentProjection, sectionUpstreamPoint);

            var midpointToDownstreamInstrumentProjection = CalculateIntersectionPoint(instrumentPoint, sectionMidpoint, sectionDownstreamPoint);
            var distanceFromDownstreamProjectionToDownstream = GetDistance(midpointToDownstreamInstrumentProjection, sectionDownstreamPoint);

            var distanceFromUpstreamToMidpoint = GetDistance(sectionUpstreamPoint, sectionMidpoint);
            var distanceFromDownstreamToMidpoint = GetDistance(sectionDownstreamPoint, sectionMidpoint);

            // Is `upstreamToMidpointInstrumentProjection` closer to upstream than midpoint?
            var c1 = distanceFromUpstreamToUpstreamProjection < distanceFromUpstreamToMidpoint;
            
            // Is `midpointToDownstreamInstrumentProjection` farther from downstream than midpoint? 
            var c2 = distanceFromDownstreamProjectionToDownstream > distanceFromDownstreamToMidpoint;

            if (!c1 && c2)
            {
                return distanceFromUpstreamToMidpoint + referenceOriginPoint.X;
            }
            if (!c2)
            {
                return distanceFromDownstreamProjectionToDownstream + distanceFromUpstreamToMidpoint + referenceOriginPoint.X;
            }
            return distanceFromUpstreamToUpstreamProjection + referenceOriginPoint.X;
        }

        public static double GetInstrumentXPoint(
            PointD referenceOriginPoint,
            PointD sectionUpstreamPoint,
            PointD sectionDownstreamPoint,
            PointD instrumentCoordinate)
        {
            var perpendicularPoint = CalculateIntersectionPoint(instrumentCoordinate, sectionUpstreamPoint, sectionDownstreamPoint);

            var distance = GetDistance(perpendicularPoint, sectionUpstreamPoint);

            return distance + referenceOriginPoint.X;
        }

        private static PointD CalculateIntersectionPoint(PointD refPoint, PointD point1, PointD point2)
        {
            var a = (point2.Y - point1.Y) / (point2.X - point1.X);
            var b = ((point1.Y * point2.X) - (point1.X * point2.Y)) / (point2.X - point1.X);

            var m = -1 / a;
            var n = (refPoint.Y + (((point2.X - point1.X) / (point2.Y - point1.Y)) * refPoint.X));

            var xA = (n - b) / (a - m);
            var yA = a * xA + b;

            xA = double.IsNaN(xA) ? 0 : xA;
            yA = double.IsNaN(yA) ? 0 : yA;

            return new(xA, yA);
        }

        private static double GetDistance(PointD point1, PointD point2)
        {
            return Math.Sqrt(Math.Pow(point1.X - point2.X, 2) + Math.Pow(point1.Y - point2.Y, 2));
        }
    }
}